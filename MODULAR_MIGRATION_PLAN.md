# EVO App Modular System Migration Plan

## Overview

This document outlines the comprehensive plan to migrate the EVO app from its current monolithic dependency injection setup to the modular system provided by flutter-common-package.

## Current State Analysis

### ✅ What's Already Good
- Flutter-common-package integrated (v cb7f278ce0e5540e89197a4b85cff0c0045679ca)
- GetIt dependency injection setup
- Well-organized feature-based architecture
- Comprehensive initialization in `prepare_for_app_initiation.dart`
- Clean separation of concerns

### ❌ What Needs Improvement
- No modular system implementation
- Monolithic initialization process
- Tight coupling between features
- Difficult to test individual components
- No lazy loading of features

## Migration Strategy

### Phase 1: Foundation Setup ✅ COMPLETED
- [x] Create module structure (`lib/modules/`)
- [x] Define module names (`EvoAppModuleNames`)
- [x] Create core modules (Auth, Biometric, Navigation)
- [x] Create UI modules (Theme)
- [x] Create utility modules (Validation, Logging, PrivilegeAction)
- [x] Create feature modules (PIN)
- [x] Create modular initialization (`app_initialization_modular.dart`)
- [x] Create modular main file (`main_modular.dart`)

### Phase 2: Core Module Implementation 🔄 IN PROGRESS

#### 2.1 Authentication Module
**Files Created:**
- `lib/modules/core/auth_module.dart`

**Dependencies Managed:**
- `AuthenticationRepo` & `AuthenticationRepoImpl`
- `UserRepo` & `UserRepoImpl`
- `JwtHelper` & `MockJwtHelper`
- `EvoLocalStorageHelper` & `EvoSecureStorageHelperImpl`

#### 2.2 Biometric Module
**Files Created:**
- `lib/modules/core/biometric_module.dart`

**Dependencies Managed:**
- `BiometricsAuthenticate` & `BiometricAuthenticateImpl`
- `TsBioDetectChanged`
- `BiometricsTokenModule`
- `BiometricStatusHelper` & `BiometricStatusHelperImpl`
- `BiometricFunctions`
- `BiometricTypeHelper`
- `RequestUserActiveBiometricUtil`
- `RequestUserActivateBiometricHandler`

#### 2.3 Navigation Module
**Files Created:**
- `lib/modules/core/navigation_module.dart`

**Dependencies Managed:**
- `CommonNavigator` & `EvoRouterNavigator`
- `EvoNavigatorTypeFactory`
- `EvoNavigatorObserver`

### Phase 3: Feature Module Implementation 📋 TODO

#### 3.1 Login Module
**Target File:** `lib/modules/feature/login_module.dart`
**Dependencies to Manage:**
- Login-related cubits and handlers
- Old device login utilities
- New device verification

#### 3.2 Main Screen Module
**Target File:** `lib/modules/feature/main_screen_module.dart`
**Dependencies to Manage:**
- Main screen bloc
- Card page cubits
- Home page cubit
- Usage page cubit
- Payment summary components

#### 3.3 Profile Module
**Target File:** `lib/modules/feature/profile_module.dart`
**Dependencies to Manage:**
- Profile screen cubit
- User management
- Settings management

#### 3.4 eKYC Module
**Target File:** `lib/modules/feature/ekyc_module.dart`
**Dependencies to Manage:**
- eKYC intro and selfie components
- Camera permission handling
- Document verification

#### 3.5 Transaction Details Module
**Target File:** `lib/modules/feature/transaction_details_module.dart`
**Dependencies to Manage:**
- Transaction details presentation layer
- Transaction data repositories
- Transaction use cases

### Phase 4: Data Layer Modularization 📋 TODO

#### 4.1 API Module
**Target File:** `lib/modules/data/api_module.dart`
**Dependencies to Manage:**
- HTTP interceptors (Auth, Error, Logging)
- API service configurations
- Request/Response models

#### 4.2 Repository Module
**Target File:** `lib/modules/data/repository_module.dart`
**Dependencies to Manage:**
- All repository implementations
- Data source abstractions
- Cache management

### Phase 5: Testing Strategy 📋 TODO

#### 5.1 Module Testing
- Unit tests for each module
- Integration tests for module interactions
- Mock modules for testing

#### 5.2 Migration Testing
- Parallel testing of old vs new initialization
- Performance comparison
- Memory usage analysis

## Implementation Steps

### Step 1: Gradual Migration
1. Keep existing `prepare_for_app_initiation.dart` as fallback
2. Create `main_modular.dart` for testing modular approach
3. Gradually move dependencies from old to new system
4. Test each module independently

### Step 2: Feature Flags
```dart
// In main.dart
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  const bool useModularSystem = bool.fromEnvironment('USE_MODULAR', defaultValue: false);
  
  if (useModularSystem) {
    await initializeEvoApplication();
  } else {
    await prepareForAppInitiation();
  }
  
  runApp(const MyApp());
}
```

### Step 3: Module Validation
```dart
// Add to app_initialization_modular.dart
void validateModuleIntegrity() {
  final moduleInfo = getEvoModuleInfo();
  
  // Validate all expected modules are loaded
  final expectedModules = [
    EvoAppModuleNames.auth,
    EvoAppModuleNames.biometric,
    EvoAppModuleNames.navigation,
    // ... add all expected modules
  ];
  
  for (final module in expectedModules) {
    assert(
      isModuleInitialized(module),
      'Module $module is not initialized'
    );
  }
}
```

## Benefits of Modular System

### 1. **Improved Testability**
- Each module can be tested in isolation
- Mock modules for testing
- Easier dependency injection for tests

### 2. **Better Performance**
- Lazy loading of modules
- Reduced memory footprint
- Faster app startup

### 3. **Enhanced Maintainability**
- Clear separation of concerns
- Easier to add/remove features
- Better code organization

### 4. **Scalability**
- Easy to add new modules
- Team can work on different modules independently
- Reduced merge conflicts

### 5. **Dependency Management**
- Automatic dependency resolution
- Circular dependency detection
- Clear dependency graph

## Migration Checklist

### Phase 1: Foundation ✅
- [x] Module structure created
- [x] Core modules implemented
- [x] Modular initialization created
- [x] Basic testing setup

### Phase 2: Core Features 🔄
- [ ] Complete authentication module testing
- [ ] Complete biometric module testing
- [ ] Complete navigation module testing
- [ ] Add remaining core modules

### Phase 3: Feature Migration 📋
- [ ] Migrate login feature
- [ ] Migrate main screen feature
- [ ] Migrate profile feature
- [ ] Migrate eKYC feature
- [ ] Migrate transaction details feature

### Phase 4: Data Layer 📋
- [ ] Create API module
- [ ] Create repository module
- [ ] Migrate all data sources

### Phase 5: Testing & Validation 📋
- [ ] Comprehensive module testing
- [ ] Performance testing
- [ ] Memory usage validation
- [ ] Production readiness check

## Next Steps

1. **Test Current Implementation**
   ```bash
   # Run with modular system
   flutter run --dart-define=USE_MODULAR=true lib/main_modular.dart
   
   # Run tests
   flutter test
   ```

2. **Add More Feature Modules**
   - Start with login module
   - Then main screen module
   - Gradually migrate all features

3. **Performance Monitoring**
   - Add module initialization timing
   - Monitor memory usage
   - Track app startup time

4. **Documentation**
   - Document each module's purpose
   - Create module dependency diagrams
   - Write migration guides for team

## Conclusion

The modular system will significantly improve the EVO app's architecture, making it more maintainable, testable, and scalable. The migration should be done gradually to ensure stability and allow for proper testing at each step.

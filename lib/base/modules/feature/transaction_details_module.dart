import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/transaction_details/transaction_details_cubit.dart';

/// Transaction Details module that provides transaction functionality dependencies.
///
/// This module handles transaction details display, transaction history,
/// and transaction-related utilities. It depends on user and common repositories.
class EvoTransactionDetailsModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.transactionDetails;

  @override
  List<Type> get dependencies => [
    TransactionDetailsCubit,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register transaction details cubit (factory for multiple instances)
    if (!getIt.isRegistered<TransactionDetailsCubit>()) {
      getIt.registerFactory<TransactionDetailsCubit>(
        () => TransactionDetailsCubit(),
      );
    }
  }
}

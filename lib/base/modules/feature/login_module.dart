import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/login/utils/login_old_device_utils.dart';

/// Login module that provides login functionality dependencies.
///
/// This module handles user login, device verification, and old device management.
/// It depends on auth, storage, and validation modules.
class EvoLoginModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.login;

  @override
  List<Type> get dependencies => [
    LoginOldDeviceUtils,
  ];


  @override
  Future<void> register(GetIt getIt) async {
    // Register login old device utils
    if (!getIt.isRegistered<LoginOldDeviceUtils>()) {
      getIt.registerLazySingleton<LoginOldDeviceUtils>(() => LoginOldDeviceUtils());
    }
  }
}

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/login/utils/login_old_device_utils.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/functions.dart';

/// Login module that provides login functionality dependencies.
///
/// This module handles user login, device verification, and old device management.
/// It depends on auth, storage, and validation modules.
class EvoLoginModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.login;

  @override
  List<Type> get dependencies => [
    LoginCubit,
    LoginOldDeviceUtils,
    NewDeviceVerificationHandler,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register login old device utils
    if (!getIt.isRegistered<LoginOldDeviceUtils>()) {
      getIt.registerLazySingleton<LoginOldDeviceUtils>(() => LoginOldDeviceUtils());
    }

    // Register new device verification handler (factory for multiple instances)
    if (!getIt.isRegistered<NewDeviceVerificationHandler>()) {
      getIt.registerFactory<NewDeviceVerificationHandler>(
        () => NewDeviceVerificationHandlerImpl(
          authRepo: getIt.get<AuthenticationRepo>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          evoUtilFunction: getIt.get<EvoUtilFunction>(),
        ),
      );
    }

    // Register login cubit (factory for multiple instances)
    if (!getIt.isRegistered<LoginCubit>()) {
      getIt.registerFactory<LoginCubit>(
        () => LoginCubit(
          authRepo: getIt.get<AuthenticationRepo>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          loginOldDeviceUtils: getIt.get<LoginOldDeviceUtils>(),
          newDeviceVerificationHandler: getIt.get<NewDeviceVerificationHandler>(),
        ),
      );
    }
  }
}

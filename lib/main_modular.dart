import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:go_router/go_router.dart';

import 'app_initialization.dart';
import 'app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize the modular system
  await initializeApplication(
    locale: const Locale('en', 'US'),
    isProduction: const bool.fromEnvironment('dart.vm.product'),
  );

  // Lock screen orientation to portrait
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Get the router from the dependency injection container
    final router = getIt.get<GoRouter>();

    return MaterialApp.router(
      title: 'Modular App',
      routerConfig: router,
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
          child: child!,
        );
      },
    );
  }
}

// Copyright (c) 2024 Trusting Social. All rights reserved.

/// Constants for module names used throughout the EVO application.
///
/// This class centralizes all module names to ensure consistency
/// and avoid typos when referencing modules by name.
class EvoAppModuleNames {
  // Core modules
  static const String authorization = 'evo_authorization'; // Combined auth + session
  static const String navigation = 'evo_navigation';
  static const String storage = 'evo_storage';
  static const String biometric = 'evo_biometric';
  static const String coreUtility = 'evo_core_utility'; // Combined AppState + EvoUtilFunction
  static const String security = 'evo_security';

  // Feature modules
  static const String login = 'evo_login';
  static const String mainScreen = 'evo_main_screen';
  static const String profile = 'evo_profile';
  static const String pin = 'evo_pin'; // Now includes validation
  static const String ekyc = 'evo_ekyc';
  static const String transactionDetails = 'evo_transaction_details';
  static const String accountActivation = 'evo_account_activation';
  static const String verifyOtp = 'evo_verify_otp';

  // Data modules
  static const String api = 'evo_api';
  static const String repository = 'evo_repository';
  // Removed cache module

  // UI modules
  static const String theme = 'evo_theme';
  static const String components = 'evo_components'; // Includes dialogs and snackbars
  static const String uiUtility = 'evo_ui_utility'; // UI utilities and helpers

  // Utility modules
  static const String logging = 'evo_logging';
  static const String featureToggle = 'evo_feature_toggle';
  static const String wrapperUtility = 'evo_wrapper_utility'; // Wrapper utilities
  // Removed validation module (moved to pin)
  // Removed performance module (not needed)
  static const String privilegeAction = 'evo_privilege_action';
}

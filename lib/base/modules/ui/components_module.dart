import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/evo_snackbar.dart';
import '../../../util/dialog_functions.dart';

/// UI Components module that provides reusable UI component dependencies.
///
/// This module handles custom widgets, dialogs, snackbars, and other UI components
/// that are used throughout the application.
class EvoComponentsModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.components;

  @override
  List<Type> get dependencies => [
    DialogFunction,
    EvoSnackBar,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register dialog functions
    if (!getIt.isRegistered<DialogFunction>()) {
      getIt.registerLazySingleton<DialogFunction>(() => DialogFunction());
    }

    // Register snackbar utilities
    if (!getIt.isRegistered<EvoSnackBar>()) {
      getIt.registerLazySingleton<EvoSnackBar>(
        () => EvoSnackBar(SnackBarWrapper()),
      );
    }

    // Additional UI components can be registered here
    // For example: custom widgets, form components, etc.
  }
}

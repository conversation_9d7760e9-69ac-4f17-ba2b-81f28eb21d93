import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/verify_otp/cubit/verify_otp_cubit.dart';
import '../../../feature/verify_otp/handler/verify_otp_handler_factory.dart';
import '../../../feature/verify_otp/verify_otp_page.dart';
import '../../../data/repository/authentication_repo.dart';

/// Verify OTP module that provides OTP verification functionality dependencies.
///
/// This module handles OTP verification, resend OTP, and OTP-related utilities.
/// It depends on authentication repository and utility modules.
class EvoVerifyOtpModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.verifyOtp;

  @override
  List<Type> get dependencies => [
    VerifyOtpCubit,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register verify OTP cubit (factory for multiple instances)
    // Note: VerifyOtpCubit uses VerifyOtpHandler which is created via factory
    if (!getIt.isRegistered<VerifyOtpCubit>()) {
      getIt.registerFactory<VerifyOtpCubit>(
        () => VerifyOtpCubit(
          verifyHandler: VerifyOtpHandlerFactory.createHandler(
            VerifyOtpType.signIn, // Default type, can be overridden when needed
            getIt.get<AuthenticationRepo>(),
          ),
        ),
      );
    }
  }
}

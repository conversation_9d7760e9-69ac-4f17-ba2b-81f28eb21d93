import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/camera_permission/cubit/camera_permission_cubit.dart';

/// eKYC module that provides eKYC functionality dependencies.
///
/// This module handles eKYC-related functionality including camera permissions
/// and other eKYC utilities. Currently focused on camera permission management.
class EvoEkycModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.ekyc;

  @override
  List<Type> get dependencies => <Type>[
    CameraPermissionCubit,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register camera permission cubit (factory for multiple instances)
    // The cubit uses the default CameraPermissionHandler internally
    if (!getIt.isRegistered<CameraPermissionCubit>()) {
      getIt.registerFactory<CameraPermissionCubit>(
        () => CameraPermissionCubit(),
      );
    }
  }
}

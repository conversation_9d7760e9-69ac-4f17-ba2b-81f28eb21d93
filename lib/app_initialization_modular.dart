import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter_common_package/base/module/module_names.dart';
import 'package:flutter_common_package/common_package/common_package.dart';
import 'package:flutter_common_package/data/http_client/common_http_client.dart';
import 'package:flutter_common_package/data/http_client/dio_http_client_impl.dart';
import 'package:flutter_common_package/data/http_client/dio_log_interceptor.dart';
import 'package:flutter_common_package/data/repository/ekyc/ekyc_repo.dart';
import 'package:flutter_common_package/data/repository/logging/logging_repo_impl.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:flutter_common_package/util/flutter_downloader/common_flutter_downloader.dart';

// Import consolidated modules
import 'base/modules/core/authorization_module.dart';
import 'base/modules/core/biometric_module.dart';
import 'base/modules/core/core_utility_module.dart';
import 'base/modules/core/navigation_module.dart';
import 'base/modules/core/security_module.dart';
import 'base/modules/core/storage_module.dart';
import 'base/modules/data/api_module.dart';
import 'base/modules/data/repository_module.dart';
import 'base/modules/feature/account_activation_module.dart';
import 'base/modules/feature/ekyc_module.dart';
import 'base/modules/feature/login_module.dart';
import 'base/modules/feature/main_screen_module.dart';
import 'base/modules/feature/pin_module.dart';
import 'base/modules/feature/profile_module.dart';
import 'base/modules/feature/transaction_details_module.dart';
import 'base/modules/feature/verify_otp_module.dart';
import 'base/modules/module_names.dart';
import 'base/modules/ui/components_module.dart';
import 'base/modules/ui/theme_module.dart';
import 'base/modules/ui/ui_utility_module.dart';
import 'base/modules/utility/feature_toggle_module.dart';
import 'base/modules/utility/logging_module.dart';
import 'base/modules/utility/privilege_action_module.dart';
import 'base/modules/utility/wrapper_utility_module.dart';
import 'prepare_for_app_initiation.dart' as prep;
import 'data/repository/common_repo.dart';
import 'data/repository/common_repo_impl.dart';
import 'data/repository/mock_ekyc_repo.dart';
import 'feature/authorization_session_expired/authorization_session_expired.dart';
import 'util/interceptor/log_event_interceptor.dart';
import 'util/interceptor/unauthorized_interceptor.dart';
import 'data/repository/authentication_repo.dart';
import 'data/repository/authentication_repo_impl.dart';
import 'data/response/reset_pin_entity.dart';
import 'util/token_utils/jwt_helper.dart';

GetIt getIt = GetIt.instance;

/// Initializes the EVO application with modular architecture.
///
/// This function sets up the modular architecture by:
/// 1. Initializing essential common package modules
/// 2. Registering and initializing EVO app modules
/// 3. Setting up additional services and interceptors
/// 4. Handling module dependencies automatically
Future<void> initializeEvoApplication({
  Locale? locale,
  bool isProduction = false,
}) async {
  final Locale defaultLocale = locale ?? const Locale('vi');

  // Step 1: Initialize essential common package modules
  await initCommonPackage(
    locale: defaultLocale,
    features: <String>[
      CommonPackageModuleNames.core, // Essential storage, UUID, etc.
      CommonPackageModuleNames.network, // HTTP client, connectivity
      CommonPackageModuleNames.ui, // Common UI components
      CommonPackageModuleNames.utility, // Utility functions
      CommonPackageModuleNames.deviceInfo, // Device information
      if (!isProduction) ...<String>[
        // Add development-only modules
        CommonPackageModuleNames.analytics, // Analytics for debugging
      ],
    ],
  );

  // Step 2: Initialize app state
  await prep.initAppState(locale: defaultLocale);

  // Step 3: Register EVO app modules
  await _registerEvoAppModules();

  // Step 4: Initialize EVO app modules in dependency order
  await _initializeEvoAppModules();

  // Step 5: Set up additional services and interceptors
  await _setupAdditionalServices();
}

/// Registers all EVO application modules.
Future<void> _registerEvoAppModules() async {
  const String source = 'evo_app';

  // Register core modules
  registerCustomModule(AuthorizationModule(), source: source);
  registerCustomModule(EvoBiometricModule(), source: source);
  registerCustomModule(EvoNavigationModule(), source: source);
  registerCustomModule(EvoStorageModule(), source: source);
  registerCustomModule(CoreUtilityModule(), source: source);
  registerCustomModule(SecurityModule(), source: source);

  // Register data modules
  registerCustomModule(EvoApiModule(), source: source);
  registerCustomModule(EvoRepositoryModule(), source: source);

  // Register UI modules
  registerCustomModule(EvoThemeModule(), source: source);
  registerCustomModule(EvoComponentsModule(), source: source);
  registerCustomModule(UiUtilityModule(), source: source);

  // Register utility modules
  registerCustomModule(FeatureToggleModule(), source: source);
  registerCustomModule(EvoLoggingModule(), source: source);
  registerCustomModule(EvoPrivilegeActionModule(), source: source);
  registerCustomModule(WrapperUtilityModule(), source: source);

  // Register feature modules
  // Note: Some feature modules are commented out due to missing dependencies
  // registerCustomModule(EvoLoginModule(), source: source);
  // registerCustomModule(EvoMainScreenModule(), source: source);
  // registerCustomModule(EvoProfileModule(), source: source);
  registerCustomModule(EvoPinModule(), source: source);
  registerCustomModule(EvoEkycModule(), source: source);
  // registerCustomModule(EvoTransactionDetailsModule(), source: source);
  // registerCustomModule(EvoAccountActivationModule(), source: source);
  // registerCustomModule(EvoVerifyOtpModule(), source: source);
}

/// Initializes EVO application modules in the correct dependency order.
Future<void> _initializeEvoAppModules() async {
  // Initialize modules in dependency order
  // The module system will automatically resolve dependencies
  await initializeSpecificModules(<String>[
    // Core modules first
    EvoAppModuleNames.storage,
    EvoAppModuleNames.authorization, // Replaces auth and authSession
    EvoAppModuleNames.biometric,
    EvoAppModuleNames.navigation,
    EvoAppModuleNames.coreUtility, // Replaces appState and evoUtilFunction
    EvoAppModuleNames.security,

    // Data modules
    EvoAppModuleNames.api,
    EvoAppModuleNames.repository,

    // UI modules
    EvoAppModuleNames.theme,
    EvoAppModuleNames.components,
    EvoAppModuleNames.uiUtility,

    // Utility modules
    EvoAppModuleNames.featureToggle,
    EvoAppModuleNames.logging,
    EvoAppModuleNames.privilegeAction,
    EvoAppModuleNames.wrapperUtility,

    // Feature modules
    EvoAppModuleNames.login,
    EvoAppModuleNames.mainScreen,
    EvoAppModuleNames.profile,
    EvoAppModuleNames.pin,
    EvoAppModuleNames.ekyc,
    EvoAppModuleNames.transactionDetails,
    EvoAppModuleNames.accountActivation,
    EvoAppModuleNames.verifyOtp,
  ]);
}

/// Sets up additional services that are not part of the modular system yet.
/// These will be gradually migrated to modules in future iterations.
Future<void> _setupAdditionalServices() async {
  // Register additional utilities
  _registerAdditionalUtilities();

  // Set up HTTP client interceptors
  await _setupHttpClientInterceptors();

  // Register additional repositories
  await _registerAdditionalRepositories();

  // Initialize Flutter downloader
  await _initializeFlutterDownloader();
}

void _registerAdditionalUtilities() {
  // All utilities are now handled by modules
  // This method is kept for future additional utilities that aren't yet modularized
}

Future<void> _setupHttpClientInterceptors() async {
  // Set up non-authentication HTTP client
  await _registerNonAuthenticationHttpClient();

  // Set up Dio interceptors
  setUpDioInterceptor();
}

Future<void> _registerAdditionalRepositories() async {
  // Register common repository
  getIt.registerLazySingleton<CommonRepo>(
      () => CommonRepoImpl(getIt.get<CommonHttpClient>()));

  // Register mock EKYC repository (TODO: remove mock)
  if (getIt.isRegistered<EkycRepo>()) {
    getIt.unregister<EkycRepo>();
    getIt.registerSingleton<EkycRepo>(MockEkycRepoImpl(
      getIt.get<CommonHttpClient>(),
    ));
  }
}

Future<void> _initializeFlutterDownloader() async {
  final CommonFlutterDownloader flutterDownloaderWrapper =
      getIt.get<CommonFlutterDownloader>();
  await flutterDownloaderWrapper.initialize();
}

/// Register non-authentication HTTP client for refresh token operations
Future<void> _registerNonAuthenticationHttpClient() async {
  /// We need a separate Dio to handle refresh token which will not apply [UnauthorizedInterceptor].
  /// Use can use it to make api call which don't need authentication (don't put access token to header)
  final Dio nonAuthenticationDio = Dio();

  if (kDebugMode) {
    nonAuthenticationDio.interceptors.add(
      DioLogInterceptor(
        responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
      ),
    );
  }

  getIt.registerLazySingleton<Dio>(
    () => nonAuthenticationDio,
    instanceName: prep.nonAuthenticationHttpClientInstance,
  );

  getIt.registerLazySingleton<CommonHttpClient>(
    () => DioClientImpl(
      getIt.get<Dio>(
        instanceName: prep.nonAuthenticationHttpClientInstance,
      ),
    ),
    instanceName: prep.nonAuthenticationHttpClientInstance,
  );
}

/// Set up Dio interceptors for authentication and logging
void setUpDioInterceptor() {
  final Dio dio = getIt.get<Dio>();
  dio.interceptors.addAll(
    <Interceptor>[
      if (kDebugMode)
        DioLogInterceptor(
          responseHeaderKeyLogged: <String>[CommonHttpClient.xRequestIdHeader],
        ),
      LogEventInterceptor(eventLogPath: LoggingRepoImpl.logUrl),
      UnauthorizedInterceptor(
        getIt<AuthorizationSessionExpiredHandler>(),
        getIt<AuthenticationRepo>(),
        getIt<prep.AppState>(),
        getIt<JwtHelper>(),
        ignoredRefreshTokenApiPath: <String>[
          AuthenticationRepoImpl.signInUrl,
        ],
        ignoredVerdictEmitUnauthorized: <String>[
          ResetPinEntity.verdictExpiredResetPinSession,
          ResetPinEntity.verdictInvalidResetPinSession,
          ResetPinEntity.verdictMissingResetPinSession,
        ],
      ),
    ],
  );
}

/// Gets information about the current module state.
Map<String, dynamic> getEvoModuleInfo() {
  return <String, dynamic>{
    'registered_modules': getRegisteredModules(),
    'initialized_modules': getInitializedModules(),
    'common_package_modules': getInitializedModules()
        .where((String module) => module.startsWith('common_'))
        .toList(),
    'evo_app_modules': getInitializedModules()
        .where((String module) => module.startsWith('evo_'))
        .toList(),
  };
}

/// Initializes additional feature modules on-demand.
Future<void> initializeEvoFeatureModules(List<String> moduleNames) async {
  await initializeSpecificModules(moduleNames);
}

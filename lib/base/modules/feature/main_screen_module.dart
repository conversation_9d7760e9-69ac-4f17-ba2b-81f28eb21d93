import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/main_screen/bloc/main_cubit.dart';
import '../../../feature/main_screen/card_page/cubits/cards_page_cubit.dart';
import '../../../feature/main_screen/card_page/cubits/recent_transactions_cubit.dart';
import '../../../feature/main_screen/home_page/cubit/home_page_cubit.dart';
import '../../../feature/main_screen/usage_page/cubit/transaction_group_cubit.dart';
import '../../../feature/main_screen/main_screen_dialog_handler/main_screen_dialog_handler.dart';
import '../../../feature/privilege_action/privilege_access_guard_module.dart';
import '../../../prepare_for_app_initiation.dart';

/// Main screen module that provides main screen functionality dependencies.
///
/// This module handles the main screen, card page, home page, usage page, and payment summary.
/// It depends on user, common repositories and other core modules.
class EvoMainScreenModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.mainScreen;

  @override
  List<Type> get dependencies => [
    MainCubit,
    CardsPageCubit,
    RecentTransactionsCubit,
    HomePageCubit,
    TransactionGroupCubit,
    MainScreenDialogHandler,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register main screen dialog handler
    if (!getIt.isRegistered<MainScreenDialogHandler>()) {
      getIt.registerLazySingleton<MainScreenDialogHandler>(() => MainScreenDialogHandler());
    }

    // Register main cubit (factory for multiple instances)
    if (!getIt.isRegistered<MainCubit>()) {
      getIt.registerFactory<MainCubit>(
        () => MainCubit(
          appState: getIt.get<AppState>(),
          mainScreenDialogHandler: getIt.get<MainScreenDialogHandler>(),
        ),
      );
    }

    // Register cards page cubit (factory for multiple instances)
    if (!getIt.isRegistered<CardsPageCubit>()) {
      getIt.registerFactory<CardsPageCubit>(
        () => CardsPageCubit(
          privilegeAccessGuardModule: getIt.get<PrivilegeAccessGuardModule>(),
        ),
      );
    }

    // Register recent transactions cubit (factory for multiple instances)
    if (!getIt.isRegistered<RecentTransactionsCubit>()) {
      getIt.registerFactory<RecentTransactionsCubit>(
        () => RecentTransactionsCubit(),
      );
    }

    // Register home page cubit (factory for multiple instances)
    if (!getIt.isRegistered<HomePageCubit>()) {
      getIt.registerFactory<HomePageCubit>(
        () => HomePageCubit(),
      );
    }

    // Register transaction group cubit (factory for multiple instances)
    if (!getIt.isRegistered<TransactionGroupCubit>()) {
      getIt.registerFactory<TransactionGroupCubit>(
        () => TransactionGroupCubit(),
      );
    }
  }
}

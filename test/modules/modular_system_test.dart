import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_common_package/init_common_package.dart';
import 'package:mocktail/mocktail.dart';

import '../../lib/app_initialization_modular.dart';
import '../../lib/modules/module_names.dart';

void main() {
  group('Modular System Tests', () {
    setUp(() async {
      // Reset GetIt before each test
      await getIt.reset();
    });

    test('should initialize all core modules successfully', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      final moduleInfo = getEvoModuleInfo();
      final initializedModules = moduleInfo['initialized_modules'] as List<String>;

      // Verify core modules are initialized
      expect(initializedModules, contains(EvoAppModuleNames.auth));
      expect(initializedModules, contains(EvoAppModuleNames.biometric));
      expect(initializedModules, contains(EvoAppModuleNames.navigation));
      expect(initializedModules, contains(EvoAppModuleNames.theme));
      expect(initializedModules, contains(EvoAppModuleNames.validation));
      expect(initializedModules, contains(EvoAppModuleNames.logging));
    });

    test('should have correct module dependencies', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert - Check that auth module dependencies are registered
      expect(isModuleInitialized(EvoAppModuleNames.auth), isTrue);
      
      // Verify auth module dependencies are available
      expect(getIt.isRegistered<AuthenticationRepo>(), isTrue);
      expect(getIt.isRegistered<UserRepo>(), isTrue);
      expect(getIt.isRegistered<JwtHelper>(), isTrue);
      expect(getIt.isRegistered<EvoLocalStorageHelper>(), isTrue);
    });

    test('should initialize biometric module with all dependencies', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.biometric), isTrue);
      
      // Verify biometric module dependencies
      expect(getIt.isRegistered<BiometricsAuthenticate>(), isTrue);
      expect(getIt.isRegistered<TsBioDetectChanged>(), isTrue);
      expect(getIt.isRegistered<BiometricStatusHelper>(), isTrue);
      expect(getIt.isRegistered<BiometricFunctions>(), isTrue);
    });

    test('should initialize navigation module correctly', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.navigation), isTrue);
      
      // Verify navigation dependencies
      expect(getIt.isRegistered<CommonNavigator>(), isTrue);
      expect(getIt.isRegistered<EvoNavigatorTypeFactory>(), isTrue);
      expect(getIt.isRegistered<EvoNavigatorObserver>(), isTrue);
    });

    test('should initialize theme module with UI dependencies', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.theme), isTrue);
      
      // Verify theme dependencies
      expect(getIt.isRegistered<CommonTextStyles>(), isTrue);
      expect(getIt.isRegistered<CommonColors>(), isTrue);
      expect(getIt.isRegistered<CommonButtonDimensions>(), isTrue);
      expect(getIt.isRegistered<CommonButtonStyles>(), isTrue);
    });

    test('should initialize validation module', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.validation), isTrue);
      
      // Verify validation dependencies
      expect(getIt.isRegistered<EvoValidator>(), isTrue);
      expect(getIt.isRegistered<MpinValidator>(), isTrue);
    });

    test('should initialize logging module', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.logging), isTrue);
      
      // Verify logging dependencies
      expect(getIt.isRegistered<EvoEventTrackingUtils>(), isTrue);
      expect(getIt.isRegistered<FeatureToggle>(), isTrue);
    });

    test('should initialize privilege action module', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.privilegeAction), isTrue);
      
      // Verify privilege action dependencies are registered as factories
      expect(getIt.isRegistered<VerifyBiometricForPrivilegeAction>(), isTrue);
      expect(getIt.isRegistered<PrivilegeAccessGuardModule>(), isTrue);
    });

    test('should initialize PIN module', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(isModuleInitialized(EvoAppModuleNames.pin), isTrue);
      
      // Verify PIN dependencies
      expect(getIt.isRegistered<ResetPinHandler>(), isTrue);
    });

    test('should provide module information correctly', () async {
      // Act
      await initializeEvoApplication(isProduction: true);
      final moduleInfo = getEvoModuleInfo();

      // Assert
      expect(moduleInfo, isA<Map<String, dynamic>>());
      expect(moduleInfo['registered_modules'], isA<List<String>>());
      expect(moduleInfo['initialized_modules'], isA<List<String>>());
      expect(moduleInfo['common_package_modules'], isA<List<String>>());
      expect(moduleInfo['evo_app_modules'], isA<List<String>>());

      final evoModules = moduleInfo['evo_app_modules'] as List<String>;
      expect(evoModules.length, greaterThan(0));
      expect(evoModules.every((module) => module.startsWith('evo_')), isTrue);
    });

    test('should handle production vs development mode correctly', () async {
      // Test production mode
      await getIt.reset();
      await initializeEvoApplication(isProduction: true);
      
      var moduleInfo = getEvoModuleInfo();
      var commonModules = moduleInfo['common_package_modules'] as List<String>;
      
      // In production, analytics module should not be initialized
      expect(commonModules.contains('common_analytics'), isFalse);

      // Test development mode
      await getIt.reset();
      await initializeEvoApplication(isProduction: false);
      
      moduleInfo = getEvoModuleInfo();
      commonModules = moduleInfo['common_package_modules'] as List<String>;
      
      // In development, analytics module should be initialized
      expect(commonModules.contains('common_analytics'), isTrue);
    });

    test('should initialize additional services correctly', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert - Check additional utilities are registered
      expect(getIt.isRegistered<EvoUtilFunction>(), isTrue);
      expect(getIt.isRegistered<DialogFunction>(), isTrue);
      expect(getIt.isRegistered<EvoFlutterWrapper>(), isTrue);
      expect(getIt.isRegistered<UrlLauncherWrapper>(), isTrue);
      expect(getIt.isRegistered<AppSettingsWrapper>(), isTrue);
      expect(getIt.isRegistered<LoginOldDeviceUtils>(), isTrue);
      expect(getIt.isRegistered<SecureDetection>(), isTrue);
      expect(getIt.isRegistered<ExitAppFeature>(), isTrue);
      expect(getIt.isRegistered<EvoSnackBar>(), isTrue);
    });

    test('should initialize session handlers correctly', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(getIt.isRegistered<AuthorizationSessionExpiredHandler>(), isTrue);
      expect(getIt.isRegistered<AuthorizationSessionExpiredPopup>(), isTrue);
      expect(getIt.isRegistered<ForceLogoutPopup>(), isTrue);
    });

    test('should initialize repositories correctly', () async {
      // Act
      await initializeEvoApplication(isProduction: true);

      // Assert
      expect(getIt.isRegistered<CommonRepo>(), isTrue);
      expect(getIt.isRegistered<EkycRepo>(), isTrue);
    });
  });

  group('Module Initialization Order Tests', () {
    test('should initialize modules in correct dependency order', () async {
      // This test ensures that dependencies are available when modules need them
      await initializeEvoApplication(isProduction: true);

      // If we get here without exceptions, dependency order is correct
      expect(isModuleInitialized(EvoAppModuleNames.auth), isTrue);
      expect(isModuleInitialized(EvoAppModuleNames.biometric), isTrue);
      expect(isModuleInitialized(EvoAppModuleNames.navigation), isTrue);
    });
  });
}

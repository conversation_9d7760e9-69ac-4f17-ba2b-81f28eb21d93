import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../../../feature/transaction_details/transaction_details_cubit.dart';
import '../module_names.dart';
import '../../../feature/transaction_details/cubit/transaction_details_cubit.dart';
import '../../../feature/transaction_details/utils/transaction_utils.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/repository/common_repo.dart';
import '../../../util/functions.dart';

/// Transaction Details module that provides transaction functionality dependencies.
///
/// This module handles transaction details display, transaction history,
/// and transaction-related utilities. It depends on user and common repositories.
class EvoTransactionDetailsModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.transactionDetails;

  @override
  List<Type> get dependencies => [
    TransactionDetailsCubit,
    TransactionUtils,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register transaction utils
    if (!getIt.isRegistered<TransactionUtils>()) {
      getIt.registerLazySingleton<TransactionUtils>(
        () => TransactionUtils(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
          evoUtilFunction: getIt.get<EvoUtilFunction>(),
        ),
      );
    }

    // Register transaction details cubit (factory for multiple instances)
    if (!getIt.isRegistered<TransactionDetailsCubit>()) {
      getIt.registerFactory<TransactionDetailsCubit>(
        () => TransactionDetailsCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
          transactionUtils: getIt.get<TransactionUtils>(),
        ),
      );
    }
  }
}

import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../data/repository/user_repo.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../util/functions.dart';

/// Account Activation module that provides account activation functionality dependencies.
///
/// This module handles account activation process, verification steps,
/// and activation-related utilities. It depends on auth and user repositories.
class EvoAccountActivationModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.accountActivation;

  @override
  List<Type> get dependencies => [
    AccountActivationCubit,
    AccountActivationUtils,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register account activation utils
    if (!getIt.isRegistered<AccountActivationUtils>()) {
      getIt.registerLazySingleton<AccountActivationUtils>(
        () => AccountActivationUtils(
          authRepo: getIt.get<AuthenticationRepo>(),
          userRepo: getIt.get<UserRepo>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          evoUtilFunction: getIt.get<EvoUtilFunction>(),
        ),
      );
    }

    // Register account activation cubit (factory for multiple instances)
    if (!getIt.isRegistered<AccountActivationCubit>()) {
      getIt.registerFactory<AccountActivationCubit>(
        () => AccountActivationCubit(
          authRepo: getIt.get<AuthenticationRepo>(),
          userRepo: getIt.get<UserRepo>(),
          accountActivationUtils: getIt.get<AccountActivationUtils>(),
        ),
      );
    }
  }
}

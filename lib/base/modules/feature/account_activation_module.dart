import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/account_activation/create_username/create_username_cubit.dart';
import '../../../feature/account_activation/mobile_number_check/mobile_number_check_cubit.dart';
import '../../../feature/account_activation/verify_email/cubit/verify_email_cubit.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../util/validator/username_validator.dart';

/// Account Activation module that provides account activation functionality dependencies.
///
/// This module handles account activation process, verification steps,
/// and activation-related utilities. It depends on auth and user repositories.
class EvoAccountActivationModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.accountActivation;

  @override
  List<Type> get dependencies => [
    CreateUsernameCubit,
    MobileNumberCheckCubit,
    VerifyEmailCubit,
    UsernameValidator,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register username validator
    if (!getIt.isRegistered<UsernameValidator>()) {
      getIt.registerLazySingleton<UsernameValidator>(() => UsernameValidator());
    }

    // Register create username cubit (factory for multiple instances)
    if (!getIt.isRegistered<CreateUsernameCubit>()) {
      getIt.registerFactory<CreateUsernameCubit>(
        () => CreateUsernameCubit(
          authRepo: getIt.get<AuthenticationRepo>(),
          usernameValidator: getIt.get<UsernameValidator>(),
        ),
      );
    }

    // Register mobile number check cubit (factory for multiple instances)
    if (!getIt.isRegistered<MobileNumberCheckCubit>()) {
      getIt.registerFactory<MobileNumberCheckCubit>(
        () => MobileNumberCheckCubit(
          authRepo: getIt.get<AuthenticationRepo>(),
        ),
      );
    }

    // Register verify email cubit (factory for multiple instances)
    if (!getIt.isRegistered<VerifyEmailCubit>()) {
      getIt.registerFactory<VerifyEmailCubit>(
        () => VerifyEmailCubit(),
      );
    }
  }
}

// Copyright (c) 2024 Trusting Social. All rights reserved.

/// Constants for module names used throughout the EVO application.
///
/// This class centralizes all module names to ensure consistency
/// and avoid typos when referencing modules by name.
class EvoAppModuleNames {
  // Core modules
  static const String auth = 'evo_auth';
  static const String navigation = 'evo_navigation';
  static const String storage = 'evo_storage';
  static const String biometric = 'evo_biometric';
  
  // Feature modules
  static const String login = 'evo_login';
  static const String mainScreen = 'evo_main_screen';
  static const String profile = 'evo_profile';
  static const String pin = 'evo_pin';
  static const String ekyc = 'evo_ekyc';
  static const String transactionDetails = 'evo_transaction_details';
  static const String accountActivation = 'evo_account_activation';
  static const String verifyOtp = 'evo_verify_otp';
  
  // Data modules
  static const String api = 'evo_api';
  static const String repository = 'evo_repository';
  
  // UI modules
  static const String theme = 'evo_theme';
  static const String components = 'evo_components';
  
  // Utility modules
  static const String logging = 'evo_logging';
  static const String validation = 'evo_validation';
  static const String privilegeAction = 'evo_privilege_action';
}

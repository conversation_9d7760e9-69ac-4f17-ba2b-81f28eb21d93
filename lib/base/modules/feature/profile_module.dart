import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/profile/cubit/user_profile_cubit.dart';
import '../../../feature/profile/profile_screen/cubit/user/profile_change_pin_cubit.dart';
import '../../../feature/profile/profile_screen/cubit/sign_out/sign_out_cubit.dart';
import '../../../feature/profile/profile_screen/cubit/activate_biometric/activate_biometric_cubit.dart';
import '../../../data/repository/user_repo.dart';
import '../../../data/repository/authentication_repo.dart';
import '../../../util/secure_storage_helper/secure_storage_helper.dart';
import '../../../feature/biometric/utils/biometrics_authenticate.dart';
import '../../../prepare_for_app_initiation.dart';

/// Profile module that provides user profile management dependencies.
///
/// This module handles user profile display, editing, and profile-related utilities.
/// It depends on user repository and storage modules.
class EvoProfileModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.profile;

  @override
  List<Type> get dependencies => [
    UserProfileCubit,
    ProfileChangePinCubit,
    SignOutCubit,
    ActivateBiometricCubit,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register user profile cubit (factory for multiple instances)
    if (!getIt.isRegistered<UserProfileCubit>()) {
      getIt.registerFactory<UserProfileCubit>(
        () => UserProfileCubit(
          userRepo: getIt.get<UserRepo>(),
          localStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          appState: getIt.get<AppState>(),
        ),
      );
    }

    // Register profile change pin cubit (factory for multiple instances)
    if (!getIt.isRegistered<ProfileChangePinCubit>()) {
      getIt.registerFactory<ProfileChangePinCubit>(
        () => ProfileChangePinCubit(
          userRepo: getIt.get<UserRepo>(),
          appState: getIt.get<AppState>(),
        ),
      );
    }

    // Register sign out cubit (factory for multiple instances)
    if (!getIt.isRegistered<SignOutCubit>()) {
      getIt.registerFactory<SignOutCubit>(
        () => SignOutCubit(getIt.get<AuthenticationRepo>()),
      );
    }

    // Register activate biometric cubit (factory for multiple instances)
    if (!getIt.isRegistered<ActivateBiometricCubit>()) {
      getIt.registerFactory<ActivateBiometricCubit>(
        () => ActivateBiometricCubit(
          bioAuth: getIt.get<BiometricsAuthenticate>(),
          secureStorageHelper: getIt.get<EvoLocalStorageHelper>(),
          appState: getIt.get<AppState>(),
        ),
      );
    }
  }
}

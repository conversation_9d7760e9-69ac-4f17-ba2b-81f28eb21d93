import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../util/app_settings_wrapper.dart';
import '../../../util/url_launcher_uri_wrapper.dart';
import '../../../feature/login/utils/login_old_device_utils.dart';

/// Wrapper Utility Module that provides wrapper utility dependencies.
///
/// This module handles various wrapper utilities like URL launcher,
/// app settings, and login device utilities.
class WrapperUtilityModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.wrapperUtility;

  @override
  List<Type> get dependencies => [
    UrlLauncherWrapper,
    AppSettingsWrapper,
    LoginOldDeviceUtils,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register URL launcher wrapper
    if (!getIt.isRegistered<UrlLauncherWrapper>()) {
      getIt.registerLazySingleton<UrlLauncherWrapper>(() => UrlLauncherWrapper());
    }

    // Register app settings wrapper
    if (!getIt.isRegistered<AppSettingsWrapper>()) {
      getIt.registerLazySingleton<AppSettingsWrapper>(() => AppSettingsWrapper());
    }

  }
}

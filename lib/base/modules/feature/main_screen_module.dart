import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../../../feature/main_screen/card_page/cubits/cards_page_cubit.dart';
import '../module_names.dart';
import '../../../feature/main_screen/bloc/main_cubit.dart';
import '../../../feature/main_screen/card_page/cubits/cards_page_cubit.dart';
import '../../../feature/main_screen/card_page/cubits/recent_transactions_cubit.dart';
import '../../../feature/main_screen/home_page/cubit/home_page_cubit.dart';
import '../../../feature/main_screen/usage_page/cubit/transaction_group_cubit.dart';
import '../../../feature/main_screen/main_screen_dialog_handler/main_screen_dialog_handler.dart';
import '../../../feature/privilege_action/privilege_access_guard_module.dart';
import '../../../prepare_for_app_initiation.dart';

/// Main screen module that provides main screen functionality dependencies.
///
/// This module handles the main screen, card page, home page, usage page, and payment summary.
/// It depends on user, common repositories and other core modules.
class EvoMainScreenModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.mainScreen;

  @override
  List<Type> get dependencies => [
    MainScreenBloc,
    CardsPageCubit,
    HomePageCubit,
  ];

  @override
  Future<void> register(GetIt getIt) async {

    // Register card page cubit (factory for multiple instances)
    if (!getIt.isRegistered<CardsPageCubit>()) {
      getIt.registerFactory<CardsPageCubit>(
        () => CardsPageCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }

    // Register home page cubit (factory for multiple instances)
    if (!getIt.isRegistered<HomePageCubit>()) {
      getIt.registerFactory<HomePageCubit>(
        () => HomePageCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }

    // Register usage page cubit (factory for multiple instances)
    if (!getIt.isRegistered<UsagePageCubit>()) {
      getIt.registerFactory<UsagePageCubit>(
        () => UsagePageCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }

    // Register payment summary cubit (factory for multiple instances)
    if (!getIt.isRegistered<PaymentSummaryCubit>()) {
      getIt.registerFactory<PaymentSummaryCubit>(
        () => PaymentSummaryCubit(
          userRepo: getIt.get<UserRepo>(),
          commonRepo: getIt.get<CommonRepo>(),
        ),
      );
    }
  }
}

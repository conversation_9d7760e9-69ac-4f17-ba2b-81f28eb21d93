import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:flutter_common_package/resources/resources.dart';
import 'package:flutter_common_package/widget/default_widgets.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../resources/button_dimensions.dart';
import '../../resources/button_styles.dart';
import '../../resources/input_borders.dart';
import '../../resources/resources.dart';
import '../../widget/evo_default_widget.dart';

/// UI Theme module that provides theme and styling dependencies.
///
/// This module handles app theming, colors, text styles, button styles, and default widgets.
/// It extends the common package UI components with app-specific styling.
class EvoThemeModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.theme;

  @override
  List<Type> get dependencies => [
    CommonTextStyles,
    CommonColors,
    CommonButtonDimensions,
    CommonButtonStyles,
    CommonDefaultWidgets,
    EvoInputBorders,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register text styles
    if (!getIt.isRegistered<CommonTextStyles>()) {
      getIt.registerLazySingleton<CommonTextStyles>(() => EvoTextStyles());
    }

    // Register colors
    if (!getIt.isRegistered<CommonColors>()) {
      getIt.registerLazySingleton<CommonColors>(() => EvoColors());
    }

    // Register button dimensions
    if (!getIt.isRegistered<CommonButtonDimensions>()) {
      getIt.registerLazySingleton<CommonButtonDimensions>(() => EvoButtonDimensions());
    }

    // Register button styles
    if (!getIt.isRegistered<CommonButtonStyles>()) {
      getIt.registerLazySingleton<CommonButtonStyles>(() => EvoButtonStyles());
    }

    // Register default widgets
    if (!getIt.isRegistered<CommonDefaultWidgets>()) {
      getIt.registerLazySingleton<CommonDefaultWidgets>(() => EvoDefaultWidget());
    }

    // Register input borders
    if (!getIt.isRegistered<EvoInputBorders>()) {
      getIt.registerLazySingleton<EvoInputBorders>(() => EvoInputBorders());
    }
  }
}

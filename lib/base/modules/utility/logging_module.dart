import 'package:flutter_common_package/base/module/feature_module.dart';
import 'package:get_it/get_it.dart';

import '../module_names.dart';
import '../../../feature/logging/evo_event_tracking_utils/evo_event_tracking_utils.dart';
import '../../../feature/logging/evo_event_tracking_utils/evo_event_tracking_utils_impl.dart';
import '../../../feature/logging/evo_navigator_observer.dart';

/// Logging module that provides logging and analytics dependencies.
///
/// This module handles event tracking, analytics, and navigation observers.
class EvoLoggingModule implements FeatureModule {
  @override
  String get name => EvoAppModuleNames.logging;

  @override
  List<Type> get dependencies => [
    EvoEventTrackingUtils,
    EvoNavigatorObserver,
  ];

  @override
  Future<void> register(GetIt getIt) async {
    // Register event tracking utils
    if (!getIt.isRegistered<EvoEventTrackingUtils>()) {
      getIt.registerLazySingleton<EvoEventTrackingUtils>(() => EvoEventTrackingUtilsImpl());
    }

    // Register navigator observer
    if (!getIt.isRegistered<EvoNavigatorObserver>()) {
      getIt.registerLazySingleton<EvoNavigatorObserver>(() => EvoNavigatorObserver());
    }
  }
}
